using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// PUBG-style inventory system with weight management and item stacking
/// </summary>
public class PUBGInventory : MonoBehaviour
{
    [Header("🎒 Inventory Settings")]
    public float baseCapacity = 150f;
    public float currentCapacity = 150f;
    public float maxWeight = 200f;

    [Header("📦 Current Items")]
    public List<InventoryItem> items = new List<InventoryItem>();

    [Header("🎯 Quick Access Slots")]
    public PUBGItem primaryWeapon;
    public PUBGItem secondaryWeapon;
    public PUBGItem[] quickHealSlots = new PUBGItem[4];
    public PUBGItem[] throwableSlots = new PUBGItem[2];

    [Header("📊 Inventory Stats")]
    public float currentWeight = 0f;
    public int totalItems = 0;
    public Dictionary<string, int> itemCounts = new Dictionary<string, int>();

    void Start()
    {
        UpdateInventoryStats();
    }

    public bool TryAddItem(PUBGItem item, int quantity = 1)
    {
        if (item == null) return false;

        // Check weight limit
        float totalWeight = item.weight * quantity;
        if (currentWeight + totalWeight > maxWeight)
        {
            Debug.Log($"❌ Cannot add {item.itemName} - would exceed weight limit");
            return false;
        }

        // Check if item can stack with existing items
        if (item.stackSize > 1)
        {
            InventoryItem existingItem = FindStackableItem(item);
            if (existingItem != null)
            {
                int canStack = Mathf.Min(quantity, item.stackSize - existingItem.quantity);
                if (canStack > 0)
                {
                    existingItem.quantity += canStack;
                    quantity -= canStack;

                    if (quantity <= 0)
                    {
                        UpdateInventoryStats();
                        return true;
                    }
                }
            }
        }

        // Add remaining quantity as new items
        while (quantity > 0)
        {
            int stackSize = Mathf.Min(quantity, item.stackSize);
            InventoryItem newItem = new InventoryItem(item, stackSize);
            items.Add(newItem);
            quantity -= stackSize;
        }

        // Auto-equip weapons and healing items
        AutoEquipItem(item);

        UpdateInventoryStats();
        return true;
    }

    InventoryItem FindStackableItem(PUBGItem item)
    {
        foreach (InventoryItem invItem in items)
        {
            if (invItem.itemData.itemName == item.itemName &&
                invItem.quantity < item.stackSize)
            {
                return invItem;
            }
        }
        return null;
    }

    void AutoEquipItem(PUBGItem item)
    {
        switch (item.itemType)
        {
            case PUBGItemType.Weapon:
                AutoEquipWeapon(item);
                break;

            case PUBGItemType.Healing:
                AutoEquipHealing(item);
                break;

            case PUBGItemType.Throwable:
                AutoEquipThrowable(item);
                break;
        }
    }

    void AutoEquipWeapon(PUBGItem weapon)
    {
        if (primaryWeapon == null)
        {
            primaryWeapon = weapon;
            Debug.Log($"🔫 Equipped {weapon.itemName} as primary weapon");
        }
        else if (secondaryWeapon == null)
        {
            secondaryWeapon = weapon;
            Debug.Log($"🔫 Equipped {weapon.itemName} as secondary weapon");
        }
        else
        {
            // Replace lower tier weapon
            if (weapon.rarity > primaryWeapon.rarity)
            {
                secondaryWeapon = primaryWeapon;
                primaryWeapon = weapon;
                Debug.Log($"🔫 Upgraded primary weapon to {weapon.itemName}");
            }
            else if (weapon.rarity > secondaryWeapon.rarity)
            {
                secondaryWeapon = weapon;
                Debug.Log($"🔫 Upgraded secondary weapon to {weapon.itemName}");
            }
        }
    }

    void AutoEquipHealing(PUBGItem healing)
    {
        for (int i = 0; i < quickHealSlots.Length; i++)
        {
            if (quickHealSlots[i] == null)
            {
                quickHealSlots[i] = healing;
                Debug.Log($"💊 Equipped {healing.itemName} to heal slot {i + 1}");
                return;
            }
        }

        // Replace lower priority healing item
        for (int i = 0; i < quickHealSlots.Length; i++)
        {
            if (healing.rarity > quickHealSlots[i].rarity)
            {
                quickHealSlots[i] = healing;
                Debug.Log($"💊 Upgraded heal slot {i + 1} to {healing.itemName}");
                return;
            }
        }
    }

    void AutoEquipThrowable(PUBGItem throwable)
    {
        for (int i = 0; i < throwableSlots.Length; i++)
        {
            if (throwableSlots[i] == null)
            {
                throwableSlots[i] = throwable;
                Debug.Log($"💣 Equipped {throwable.itemName} to throwable slot {i + 1}");
                return;
            }
        }
    }

    public bool RemoveItem(string itemName, int quantity = 1)
    {
        int remaining = quantity;

        for (int i = items.Count - 1; i >= 0 && remaining > 0; i--)
        {
            if (items[i].itemData.itemName == itemName)
            {
                int removeFromStack = Mathf.Min(remaining, items[i].quantity);
                items[i].quantity -= removeFromStack;
                remaining -= removeFromStack;

                if (items[i].quantity <= 0)
                {
                    items.RemoveAt(i);
                }
            }
        }

        UpdateInventoryStats();
        return remaining == 0;
    }

    public bool HasItem(string itemName, int quantity = 1)
    {
        int totalCount = 0;

        foreach (InventoryItem item in items)
        {
            if (item.itemData.itemName == itemName)
            {
                totalCount += item.quantity;
            }
        }

        return totalCount >= quantity;
    }

    public int GetItemCount(string itemName)
    {
        int count = 0;

        foreach (InventoryItem item in items)
        {
            if (item.itemData.itemName == itemName)
            {
                count += item.quantity;
            }
        }

        return count;
    }

    public void UpgradeBackpack(int capacityBonus)
    {
        currentCapacity = baseCapacity + capacityBonus;
        maxWeight = currentCapacity + 50f; // Weight limit slightly higher than capacity

        Debug.Log($"🎒 Backpack upgraded! New capacity: {currentCapacity}");
    }

    public PUBGItem GetBestHealingItem(float currentHealthPercent)
    {
        PUBGItem bestHealing = null;
        float bestEfficiency = 0f;

        foreach (InventoryItem item in items)
        {
            if (item.itemData.itemType == PUBGItemType.Healing)
            {
                // Calculate healing efficiency based on current health
                float efficiency = CalculateHealingEfficiency(item.itemData, currentHealthPercent);

                if (efficiency > bestEfficiency)
                {
                    bestEfficiency = efficiency;
                    bestHealing = item.itemData;
                }
            }
        }

        return bestHealing;
    }

    float CalculateHealingEfficiency(PUBGItem healing, float currentHealthPercent)
    {
        // Don't use healing if health is above the item's max heal threshold
        if (currentHealthPercent >= healing.maxHealthPercent)
        {
            return 0f;
        }

        float efficiency = 0f;

        // Prioritize based on health level and item type
        if (currentHealthPercent < 0.25f) // Critical health
        {
            if (healing.healingType == HealingType.MedKit)
                efficiency = 10f;
            else if (healing.healingType == HealingType.FirstAidKit)
                efficiency = 8f;
            else if (healing.healingType == HealingType.Bandage)
                efficiency = 6f;
        }
        else if (currentHealthPercent < 0.5f) // Low health
        {
            if (healing.healingType == HealingType.FirstAidKit)
                efficiency = 9f;
            else if (healing.healingType == HealingType.MedKit)
                efficiency = 7f;
            else if (healing.healingType == HealingType.Bandage)
                efficiency = 5f;
        }
        else if (currentHealthPercent < 0.75f) // Medium health
        {
            if (healing.healingType == HealingType.Bandage)
                efficiency = 8f;
            else if (healing.healingType == HealingType.EnergyDrink)
                efficiency = 6f;
            else if (healing.healingType == HealingType.Painkiller)
                efficiency = 7f;
        }
        else // High health - use boost items
        {
            if (healing.healingType == HealingType.EnergyDrink)
                efficiency = 7f;
            else if (healing.healingType == HealingType.Painkiller)
                efficiency = 8f;
            else if (healing.healingType == HealingType.AdrenalineSyringe)
                efficiency = 9f;
        }

        // Factor in use time (faster is better in combat)
        efficiency /= healing.useTime;

        return efficiency;
    }

    public bool UseHealingItem(PUBGItem healingItem)
    {
        if (RemoveItem(healingItem.itemName, 1))
        {
            Debug.Log($"💊 Used {healingItem.itemName}");
            return true;
        }
        return false;
    }

    public List<PUBGItem> GetWeapons()
    {
        List<PUBGItem> weapons = new List<PUBGItem>();

        if (primaryWeapon != null) weapons.Add(primaryWeapon);
        if (secondaryWeapon != null) weapons.Add(secondaryWeapon);

        return weapons;
    }

    public bool IsInventoryFull()
    {
        return currentWeight >= maxWeight * 0.95f; // Consider 95% as "full"
    }

    public float GetInventoryFullness()
    {
        return currentWeight / maxWeight;
    }

    void UpdateInventoryStats()
    {
        currentWeight = 0f;
        totalItems = 0;
        itemCounts.Clear();

        foreach (InventoryItem item in items)
        {
            currentWeight += item.itemData.weight * item.quantity;
            totalItems += item.quantity;

            if (itemCounts.ContainsKey(item.itemData.itemName))
            {
                itemCounts[item.itemData.itemName] += item.quantity;
            }
            else
            {
                itemCounts[item.itemData.itemName] = item.quantity;
            }
        }
    }



    /// <summary>
    /// Check if inventory has a specific item
    /// </summary>
    public bool HasItem(string itemName)
    {
        foreach (InventoryItem item in items)
        {
            if (item.itemData.itemName == itemName && item.quantity > 0)
                return true;
        }
        return false;
    }

    /// <summary>
    /// Check if inventory has a specific weapon
    /// </summary>
    public bool HasWeapon(string weaponName)
    {
        return (primaryWeapon != null && primaryWeapon.itemName == weaponName) ||
               (secondaryWeapon != null && secondaryWeapon.itemName == weaponName) ||
               HasItem(weaponName);
    }

    /// <summary>
    /// Remove item from inventory by name
    /// </summary>
    public bool RemoveItem(string itemName, int quantity = 1)
    {
        for (int i = 0; i < items.Count; i++)
        {
            if (items[i].itemData.itemName == itemName)
            {
                if (items[i].quantity >= quantity)
                {
                    items[i].quantity -= quantity;
                    if (items[i].quantity <= 0)
                    {
                        items.RemoveAt(i);
                    }
                    UpdateInventoryStats();
                    return true;
                }
            }
        }
        return false;
    }

    public void DebugPrintInventory()
    {
        Debug.Log($"📦 Inventory ({currentWeight:F1}/{maxWeight:F1} weight):");

        foreach (InventoryItem item in items)
        {
            Debug.Log($"  - {item.itemData.itemName} x{item.quantity} ({item.itemData.weight * item.quantity:F1} weight)");
        }

        if (primaryWeapon != null)
            Debug.Log($"🔫 Primary: {primaryWeapon.itemName}");
        if (secondaryWeapon != null)
            Debug.Log($"🔫 Secondary: {secondaryWeapon.itemName}");
    }
}

/// <summary>
/// Inventory item wrapper with quantity
/// </summary>
[System.Serializable]
public class InventoryItem
{
    public PUBGItem itemData;
    public int quantity;

    public InventoryItem(PUBGItem item, int qty)
    {
        itemData = item;
        quantity = qty;
    }
}

/// <summary>
/// PUBG Armor System
/// </summary>
public class PUBGArmorSystem : MonoBehaviour
{
    [Header("🛡️ Current Armor")]
    public PUBGItem currentHelmet;
    public PUBGItem currentVest;

    [Header("🩺 Armor Durability")]
    public float helmetDurability = 100f;
    public float vestDurability = 100f;
    public float maxDurability = 100f;

    public void EquipHelmet(PUBGItem helmet)
    {
        if (helmet.isHelmet)
        {
            currentHelmet = helmet;
            helmetDurability = maxDurability;
            Debug.Log($"🪖 Equipped {helmet.itemName}");
        }
    }

    public void EquipVest(PUBGItem vest)
    {
        if (vest.isVest)
        {
            currentVest = vest;
            vestDurability = maxDurability;
            Debug.Log($"🦺 Equipped {vest.itemName}");
        }
    }

    public float CalculateDamageReduction(bool isHeadshot = false)
    {
        float reduction = 0f;

        if (isHeadshot && currentHelmet != null && helmetDurability > 0)
        {
            reduction = currentHelmet.damageReduction;
        }
        else if (!isHeadshot && currentVest != null && vestDurability > 0)
        {
            reduction = currentVest.damageReduction;
        }

        return reduction;
    }

    public float TakeDamage(float damage, bool isHeadshot = false)
    {
        float actualDamage = damage;
        float damageReduction = 0f;

        if (isHeadshot && currentHelmet != null && helmetDurability > 0)
        {
            // Calculate helmet damage reduction
            damageReduction = currentHelmet.damageReduction;
            actualDamage = damage * (1f - damageReduction);

            // Helmet takes durability damage
            float durabilityLoss = damage * 0.3f; // Helmets are more durable
            helmetDurability -= durabilityLoss;

            if (helmetDurability <= 0)
            {
                Debug.Log($"💥 {currentHelmet.itemName} destroyed!");

                // Reward agent for using armor effectively
                SquadMateAgent agent = GetComponent<SquadMateAgent>();
                if (agent != null)
                {
                    agent.AddReward(0.1f); // Small reward for armor absorption
                }

                currentHelmet = null;
                helmetDurability = 0f;
            }

            Debug.Log($"🪖 Helmet absorbed {damage - actualDamage:F1} damage (Durability: {helmetDurability:F0})");
        }
        else if (!isHeadshot && currentVest != null && vestDurability > 0)
        {
            // Calculate vest damage reduction
            damageReduction = currentVest.damageReduction;
            actualDamage = damage * (1f - damageReduction);

            // Vest takes durability damage
            float durabilityLoss = damage * 0.4f; // Vests degrade faster
            vestDurability -= durabilityLoss;

            if (vestDurability <= 0)
            {
                Debug.Log($"💥 {currentVest.itemName} destroyed!");

                // Reward agent for using armor effectively
                SquadMateAgent agent = GetComponent<SquadMateAgent>();
                if (agent != null)
                {
                    agent.AddReward(0.1f); // Small reward for armor absorption
                }

                currentVest = null;
                vestDurability = 0f;
            }

            Debug.Log($"🦺 Vest absorbed {damage - actualDamage:F1} damage (Durability: {vestDurability:F0})");
        }

        return actualDamage;
    }

    /// <summary>
    /// Get total damage reduction from all armor
    /// </summary>
    public float GetTotalDamageReduction(bool isHeadshot = false)
    {
        if (isHeadshot && currentHelmet != null && helmetDurability > 0)
        {
            return currentHelmet.damageReduction;
        }
        else if (!isHeadshot && currentVest != null && vestDurability > 0)
        {
            return currentVest.damageReduction;
        }

        return 0f;
    }

    /// <summary>
    /// Get armor priority for AI decision making
    /// </summary>
    public float GetArmorPriority(PUBGItem armorItem)
    {
        float priority = 1f;

        if (armorItem.isHelmet)
        {
            if (currentHelmet == null)
            {
                priority += 3f; // High priority for no helmet
            }
            else if (armorItem.armorLevel > currentHelmet.armorLevel)
            {
                priority += 2f; // Upgrade priority
            }
            else if (helmetDurability < 30f)
            {
                priority += 1.5f; // Replace damaged helmet
            }
        }
        else if (armorItem.isVest)
        {
            if (currentVest == null)
            {
                priority += 3f; // High priority for no vest
            }
            else if (armorItem.armorLevel > currentVest.armorLevel)
            {
                priority += 2f; // Upgrade priority
            }
            else if (vestDurability < 30f)
            {
                priority += 1.5f; // Replace damaged vest
            }
        }

        // Bonus for higher level armor
        priority += armorItem.armorLevel * 0.5f;

        return priority;
    }

    public bool HasArmor()
    {
        return (currentHelmet != null && helmetDurability > 0) ||
               (currentVest != null && vestDurability > 0);
    }

    public int GetArmorLevel()
    {
        int helmetLevel = (currentHelmet != null && helmetDurability > 0) ? currentHelmet.armorLevel : 0;
        int vestLevel = (currentVest != null && vestDurability > 0) ? currentVest.armorLevel : 0;

        return Mathf.Max(helmetLevel, vestLevel);
    }
}
